import os
import sys
import logging
from xml_generator import XMLGenerator
from lxml import etree

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger()

def test_xml_generator():
    """
    测试XML生成器，特别关注HMI/mode固定内容部分的ID是否得到保留
    """
    logger.info("开始测试XML生成器...")
    
    # 设置输出文件路径
    output_file = "test_output.xml"
    
    # 实例化XML生成器
    xml_generator = XMLGenerator(
        template_filepath="template2.xml",
        output_filepath=output_file,
        excel_filepath="manualRow_2.xlsx",
        logger=logger
    )
    
    # 生成XML
    success = xml_generator.generate_xml()
    if not success:
        logger.error("XML生成失败！")
        return False
    
    logger.info(f"XML生成成功，输出文件：{output_file}")
    
    # 验证生成的XML中HMI/mode固定内容部分的ID是否保持不变
    try:
        tree = etree.parse(output_file)
        root = tree.getroot()
        
        # 验证MultilingualText
        expected_ids = {
            'MultilingualText': {'1', '5', '8', 'C', 'F'},
            'MultilingualTextItem': {'2', '3', '6', '7', '9', 'A', 'D', 'E', '10', '11'},
            'SW.Blocks.CompileUnit': {'4', 'B', '12'}
        }
        
        # 检查HMI/mode固定内容部分的所有元素
        for tag, expected_id_set in expected_ids.items():
            elements = root.findall(f".//{tag}")
            found_ids = set()
            for elem in elements:
                id_val = elem.get('ID')
                if id_val in expected_id_set:
                    found_ids.add(id_val)
                    logger.info(f"找到期望的ID值：{tag} ID='{id_val}'")
            
            # 检查是否找到了所有期望的ID
            missing_ids = expected_id_set - found_ids
            if missing_ids:
                logger.error(f"缺少期望的ID值：元素类型 {tag}，缺少的ID：{missing_ids}")
            else:
                logger.info(f"元素类型 {tag} 的所有期望ID都已找到")
        
        logger.info("验证完成！")
        return True
    except Exception as e:
        logger.error(f"验证XML时发生错误：{e}", exc_info=True)
        return False

if __name__ == "__main__":
    test_xml_generator() 